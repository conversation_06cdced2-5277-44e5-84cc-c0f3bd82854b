# Vertex AI 流式响应修复报告

## 问题描述

在请求 Vertex AI 渠道的 `/v1/messages` 时，发现了两个主要问题：

1. **流请求返回客户端无法校验是否结束，猜测是 usage 字段没有传**
2. **多次响应统一消息** - 客户端打印的响应显示同样的消息被重复处理多次

## 问题分析

通过分析日志文件 `logs/done-hub.log`，发现了问题的根本原因：

### 问题1：流式响应格式不匹配
- **VertexAI 返回的是原始 JSON 数组格式**，不是标准的 SSE (Server-Sent Events) 格式
- 代码期望的是 `data: {...}` 格式，但实际收到的是直接的 JSON 对象
- 所有流式数据都被跳过（日志显示 "No data prefix, skipping"）

### 问题2：没有任何流式事件被发送
- 因为格式不匹配，所有 JSON 对象都被忽略
- 客户端没有收到任何 Claude 格式的流式事件
- 导致客户端无法判断响应是否结束，也没有 usage 信息

### 日志证据
```
2025/07/25 - 16:22:49	INFO	[SYS] | GeminiClaudeStreamHandler: Processing raw line: [{
2025/07/25 - 16:22:49	INFO	[SYS] | GeminiClaudeStreamHandler: No data prefix, skipping
```

## 修复方案

### 1. 正确处理 VertexAI 的原始 JSON 数组格式

**修复文件**: `providers/vertexai/category/gemini_claude.go`

#### 核心修复：

1. **恢复缓冲区处理逻辑**：
   ```go
   type GeminiClaudeStreamHandler struct {
       Usage           *types.Usage
       Request         *claude.ClaudeRequest
       Prefix          string
       ModelName       string
       buffer          []byte // 用于累积 JSON 数据
       messageStarted  bool   // 标记是否已发送 message_start
       messageId       string // 消息ID，保持一致性
   }
   ```

2. **修复流式数据处理**：
   - 移除对 `data: ` 前缀的依赖
   - 直接处理 VertexAI 的原始 JSON 数组格式
   - 累积数据并解析完整的 JSON 对象

3. **智能 JSON 解析**：
   - 支持单个 JSON 对象解析
   - 支持 JSON 数组解析
   - 支持流式 JSON 对象分割

4. **完整的流式事件生成**：
   - 正确发送 `message_start` 事件
   - 发送 `content_block_start/delta/stop` 事件
   - 在检测到 `finishReason` 时发送 `message_delta` 和 `message_stop` 事件

5. **Usage 信息正确传递**：
   - 实时更新 usage 信息
   - 在最终的 `message_delta` 事件中包含完整的 usage 数据
   - 确保客户端能够正确判断流式响应结束

### 2. 关键修复点

#### 防止重复 message_start 事件
```go
// 只在第一次处理时发送 message_start 事件
if !h.messageStarted {
    h.messageStarted = true
    // 发送 message_start 事件
}
```

#### 正确的 finishReason 检测
```go
// 检查是否是最后一个响应
if candidate.FinishReason != nil && *candidate.FinishReason != "" {
    isLastResponse = true
}
```

#### Usage 信息的正确传递
```go
// 保存最后的 usage 信息
h.lastUsage = &types.Usage{
    PromptTokens:     h.Usage.PromptTokens,
    CompletionTokens: h.Usage.CompletionTokens,
    TotalTokens:      h.Usage.TotalTokens,
    CompletionTokensDetails: types.CompletionTokensDetails{
        ReasoningTokens: h.Usage.CompletionTokensDetails.ReasoningTokens,
    },
}
```

## 测试验证

创建了测试脚本 `test_vertex_ai_stream.sh` 用于验证修复效果：

```bash
./test_vertex_ai_stream.sh
```

## 预期效果

修复后的流式响应应该：

1. **正确的事件序列**：
   - `message_start` (只发送一次)
   - `content_block_start`
   - `content_block_delta` (可能多次)
   - `content_block_stop`
   - `message_delta` (包含完整的 usage 信息，只在最后发送)
   - `message_stop` (只发送一次)

2. **完整的 Usage 信息**：
   - `input_tokens`: 输入 token 数量
   - `output_tokens`: 输出 token 数量
   - 客户端可以根据这些信息判断响应是否完整

3. **无重复消息**：
   - 每个 JSON 对象只被处理一次
   - 避免重复的内容块和事件

## 注意事项

1. 确保 Vertex AI 渠道配置正确
2. 测试时需要有效的 API 密钥
3. 建议在不同的模型和请求类型下进行测试
4. 监控日志以确认修复效果

## 相关文件

- `providers/vertexai/category/gemini_claude.go` - 主要修复文件
- `test_vertex_ai_stream.sh` - 测试脚本
- `logs/done-hub.log` - 运行日志
