# Vertex AI 流式响应修复报告

## 问题描述

在请求 Vertex AI 渠道的 `/v1/messages` 时，发现了两个主要问题：

1. **流请求返回客户端无法校验是否结束，猜测是 usage 字段没有传**
2. **多次响应统一消息** - 客户端打印的响应显示同样的消息被重复处理多次

## 问题分析

### 问题1：Usage 字段处理
- VertexAI 返回的是 JSON 数组格式，而不是标准的 SSE (Server-Sent Events) 格式
- 在流式响应中，usage 字段没有正确传递到最终的 `message_delta` 事件中
- 客户端依赖 usage 字段来判断流式响应是否结束

### 问题2：重复消息处理
- `GeminiClaudeStreamHandler` 的 JSON 解析逻辑存在问题
- 缓冲区管理不当，导致同一个 JSON 对象被重复处理
- 缺少对 `message_start` 事件的重复发送控制

## 修复方案

### 1. 改进流式响应处理逻辑

**文件**: `providers/vertexai/category/gemini_claude.go`

#### 主要修改：

1. **增加状态跟踪字段**：
   ```go
   type GeminiClaudeStreamHandler struct {
       Usage           *types.Usage
       Request         *claude.ClaudeRequest
       Prefix          string
       ModelName       string
       buffer          []byte
       messageStarted  bool   // 标记是否已发送 message_start
       lastUsage       *types.Usage // 保存最后的 usage 信息
       processedCount  int    // 已处理的对象计数
   }
   ```

2. **改进 JSON 解析逻辑**：
   - 修复缓冲区管理，确保每个 JSON 对象只被处理一次
   - 添加递归解析支持，处理连续的 JSON 对象
   - 增加处理计数器，便于调试

3. **修复 Usage 字段传递**：
   - 在每次处理 Gemini 响应时保存 usage 信息
   - 在最后的 `message_delta` 事件中包含完整的 usage 信息
   - 确保客户端能够正确识别流式响应结束

4. **添加 SSE 格式支持**：
   - 将响应格式化为标准的 SSE 格式：`event: <event_type>\ndata: <json_data>\n\n`
   - 确保与 Claude API 格式兼容

5. **改进结束条件检测**：
   - 正确检测 `finishReason` 字段（使用指针类型）
   - 只在检测到结束条件时发送 `message_delta` 和 `message_stop` 事件

### 2. 关键修复点

#### 防止重复 message_start 事件
```go
// 只在第一次处理时发送 message_start 事件
if !h.messageStarted {
    h.messageStarted = true
    // 发送 message_start 事件
}
```

#### 正确的 finishReason 检测
```go
// 检查是否是最后一个响应
if candidate.FinishReason != nil && *candidate.FinishReason != "" {
    isLastResponse = true
}
```

#### Usage 信息的正确传递
```go
// 保存最后的 usage 信息
h.lastUsage = &types.Usage{
    PromptTokens:     h.Usage.PromptTokens,
    CompletionTokens: h.Usage.CompletionTokens,
    TotalTokens:      h.Usage.TotalTokens,
    CompletionTokensDetails: types.CompletionTokensDetails{
        ReasoningTokens: h.Usage.CompletionTokensDetails.ReasoningTokens,
    },
}
```

## 测试验证

创建了测试脚本 `test_vertex_ai_stream.sh` 用于验证修复效果：

```bash
./test_vertex_ai_stream.sh
```

## 预期效果

修复后的流式响应应该：

1. **正确的事件序列**：
   - `message_start` (只发送一次)
   - `content_block_start`
   - `content_block_delta` (可能多次)
   - `content_block_stop`
   - `message_delta` (包含完整的 usage 信息，只在最后发送)
   - `message_stop` (只发送一次)

2. **完整的 Usage 信息**：
   - `input_tokens`: 输入 token 数量
   - `output_tokens`: 输出 token 数量
   - 客户端可以根据这些信息判断响应是否完整

3. **无重复消息**：
   - 每个 JSON 对象只被处理一次
   - 避免重复的内容块和事件

## 注意事项

1. 确保 Vertex AI 渠道配置正确
2. 测试时需要有效的 API 密钥
3. 建议在不同的模型和请求类型下进行测试
4. 监控日志以确认修复效果

## 相关文件

- `providers/vertexai/category/gemini_claude.go` - 主要修复文件
- `test_vertex_ai_stream.sh` - 测试脚本
- `logs/done-hub.log` - 运行日志
