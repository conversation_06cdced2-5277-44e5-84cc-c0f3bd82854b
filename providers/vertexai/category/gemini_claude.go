package category

import (
	"done-hub/common"
	"done-hub/common/logger"
	"done-hub/common/requester"
	"done-hub/providers/base"
	"done-hub/providers/claude"
	"done-hub/providers/gemini"
	"done-hub/types"
	"encoding/json"
	"errors"
	"net/http"
	"strings"
)

func init() {
	CategoryMap["gemini-claude"] = &Category{
		Category:                  "gemini-claude",
		ChatComplete:              ConvertGeminiClaudeFromChatOpenai,
		ResponseChatComplete:      ConvertGeminiClaudeToChatOpenai,
		ResponseChatCompleteStrem: GeminiClaudeChatCompleteStrem,
		ErrorHandler:              gemini.RequestErrorHandle(""),
		GetModelName:              GetGeminiClaudeModelName,
		GetOtherUrl:               getGeminiClaudeOtherUrl,
	}
}

// ConvertGeminiClaudeFromChatOpenai 将 ChatCompletion 请求转换为 Gemini 格式
// 注意：这个函数名有误导性，实际上是为了兼容现有接口
// 在 VertexAI Claude 场景下，我们会直接传入 Claude 请求
func ConvertGeminiClaudeFromChatOpenai(request *types.ChatCompletionRequest) (any, *types.OpenAIErrorWithStatusCode) {
	// 这个函数实际上不会被调用，因为我们在 relay_claude.go 中直接处理 Claude 请求
	// 但为了接口兼容性保留
	return nil, common.StringErrorWrapperLocal("should not be called", "internal_error", http.StatusInternalServerError)
}

// ConvertGeminiClaudeToChatOpenai 这个函数不会被使用，因为我们直接处理 Claude 响应
func ConvertGeminiClaudeToChatOpenai(provider base.ProviderInterface, response *http.Response, request *types.ChatCompletionRequest) (*types.ChatCompletionResponse, *types.OpenAIErrorWithStatusCode) {
	// 这个函数实际上不会被调用
	return nil, common.StringErrorWrapperLocal("should not be called", "internal_error", http.StatusInternalServerError)
}

// GeminiClaudeChatCompleteStrem 处理流式响应
func GeminiClaudeChatCompleteStrem(provider base.ProviderInterface, request *types.ChatCompletionRequest) requester.HandlerPrefix[string] {
	// 这个函数不会被调用，因为我们直接在 relay_claude.go 中创建 handler
	return func(rawLine *[]byte, dataChan chan string, errChan chan error) {
		errChan <- common.ErrorToOpenAIError(errors.New("should not be called"))
	}
}

// ConvertClaudeToGeminiRequest 直接将 Claude 请求转换为 Gemini 格式
func ConvertClaudeToGeminiRequest(claudeRequest *claude.ClaudeRequest) (*gemini.GeminiChatRequest, *types.OpenAIErrorWithStatusCode) {
	geminiRequest := &gemini.GeminiChatRequest{
		Model:    claudeRequest.Model,
		Contents: make([]gemini.GeminiChatContent, 0),
		GenerationConfig: gemini.GeminiChatGenerationConfig{
			MaxOutputTokens: claudeRequest.MaxTokens,
			Temperature:     claudeRequest.Temperature,
			TopP:            claudeRequest.TopP,
		},
		Stream: claudeRequest.Stream,
	}

	// 处理系统指令
	if claudeRequest.System != nil {
		if systemStr, ok := claudeRequest.System.(string); ok {
			geminiRequest.SystemInstruction = &gemini.GeminiChatContent{
				Parts: []gemini.GeminiPart{
					{Text: systemStr},
				},
			}
		} else if systemArray, ok := claudeRequest.System.([]interface{}); ok {
			var parts []gemini.GeminiPart
			for _, item := range systemArray {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if itemType, exists := itemMap["type"]; exists && itemType == "text" {
						if text, textExists := itemMap["text"]; textExists {
							if textStr, ok := text.(string); ok {
								parts = append(parts, gemini.GeminiPart{Text: textStr})
							}
						}
					}
				}
			}
			if len(parts) > 0 {
				geminiRequest.SystemInstruction = &gemini.GeminiChatContent{
					Parts: parts,
				}
			}
		}
	}

	// 转换消息
	for _, msg := range claudeRequest.Messages {
		role := "user"
		if msg.Role == "assistant" {
			role = "model"
		}

		var parts []gemini.GeminiPart

		// 处理消息内容
		if contentStr, ok := msg.Content.(string); ok {
			parts = append(parts, gemini.GeminiPart{Text: contentStr})
		} else if contentArray, ok := msg.Content.([]interface{}); ok {
			for _, content := range contentArray {
				if contentMap, ok := content.(map[string]interface{}); ok {
					contentType, _ := contentMap["type"].(string)

					switch contentType {
					case "text":
						if text, exists := contentMap["text"].(string); exists {
							parts = append(parts, gemini.GeminiPart{Text: text})
						}
					case "tool_use":
						if name, nameExists := contentMap["name"].(string); nameExists {
							if input, inputExists := contentMap["input"]; inputExists {
								if inputMap, ok := input.(map[string]interface{}); ok {
									parts = append(parts, gemini.GeminiPart{
										FunctionCall: &gemini.GeminiFunctionCall{
											Name: name,
											Args: inputMap,
										},
									})
								}
							}
						}
					case "tool_result":
						if content, contentExists := contentMap["content"]; contentExists {
							if toolUseId, idExists := contentMap["tool_use_id"].(string); idExists {
								parts = append(parts, gemini.GeminiPart{
									FunctionResponse: &gemini.GeminiFunctionResponse{
										Name: toolUseId,
										Response: map[string]interface{}{
											"content": content,
										},
									},
								})
							}
						}
					case "image":
						// 处理图像内容
						if source, sourceExists := contentMap["source"].(map[string]interface{}); sourceExists {
							if sourceType, typeExists := source["type"].(string); typeExists && sourceType == "base64" {
								if mediaType, mediaExists := source["media_type"].(string); mediaExists {
									if data, dataExists := source["data"].(string); dataExists {
										parts = append(parts, gemini.GeminiPart{
											InlineData: &gemini.GeminiInlineData{
												MimeType: mediaType,
												Data:     data,
											},
										})
									}
								}
							}
						}
					}
				}
			}
		}

		if len(parts) > 0 {
			geminiRequest.Contents = append(geminiRequest.Contents, gemini.GeminiChatContent{
				Role:  role,
				Parts: parts,
			})
		}
	}

	// 处理工具定义
	if len(claudeRequest.Tools) > 0 {
		var functionDeclarations []types.ChatCompletionFunction
		for _, tool := range claudeRequest.Tools {
			// 清理 InputSchema 中的 $schema 字段
			cleanedParameters := cleanSchemaForVertexAI(tool.InputSchema)

			functionDeclarations = append(functionDeclarations, types.ChatCompletionFunction{
				Name:        tool.Name,
				Description: tool.Description,
				Parameters:  cleanedParameters,
			})
		}

		// 将所有工具放在一个 GeminiChatTools 对象中
		geminiRequest.Tools = []gemini.GeminiChatTools{
			{
				FunctionDeclarations: functionDeclarations,
			},
		}
	}

	// 处理停止序列
	if len(claudeRequest.StopSequences) > 0 {
		geminiRequest.GenerationConfig.StopSequences = claudeRequest.StopSequences
	}

	return geminiRequest, nil
}

// ConvertGeminiToClaudeResponse 直接将 Gemini 响应转换为 Claude 格式
func ConvertGeminiToClaudeResponse(geminiResponse *gemini.GeminiChatResponse, model string) *claude.ClaudeResponse {
	content := make([]claude.ResContent, 0)

	if len(geminiResponse.Candidates) > 0 {
		candidate := geminiResponse.Candidates[0]

		for _, part := range candidate.Content.Parts {
			if part.Text != "" {
				content = append(content, claude.ResContent{
					Type: "text",
					Text: part.Text,
				})
			} else if part.FunctionCall != nil {
				var input interface{}
				if part.FunctionCall.Args != nil {
					input = part.FunctionCall.Args
				} else {
					input = map[string]interface{}{}
				}

				content = append(content, claude.ResContent{
					Type:  "tool_use",
					Id:    part.FunctionCall.Name + "_" + generateRandomId(),
					Name:  part.FunctionCall.Name,
					Input: input,
				})
			}
		}
	}

	// 转换停止原因
	stopReason := "end_turn"
	if len(geminiResponse.Candidates) > 0 && geminiResponse.Candidates[0].FinishReason != nil {
		switch *geminiResponse.Candidates[0].FinishReason {
		case "STOP":
			stopReason = "end_turn"
		case "MAX_TOKENS":
			stopReason = "max_tokens"
		case "SAFETY":
			stopReason = "stop_sequence"
		default:
			stopReason = "end_turn"
		}
	}

	claudeResponse := &claude.ClaudeResponse{
		Id:           "msg_" + generateRandomId(),
		Type:         "message",
		Role:         "assistant",
		Content:      content,
		Model:        model,
		StopReason:   stopReason,
		StopSequence: "",
	}

	// 处理使用量信息
	if geminiResponse.UsageMetadata != nil {
		claudeResponse.Usage = claude.Usage{
			InputTokens:  geminiResponse.UsageMetadata.PromptTokenCount,
			OutputTokens: geminiResponse.UsageMetadata.CandidatesTokenCount,
		}
	}

	return claudeResponse
}

// 以下函数已废弃，保留是为了避免编译错误
// convertChatCompletionToClaudeRequest 将 OpenAI ChatCompletion 格式转换为 Claude 格式
func convertChatCompletionToClaudeRequest(request *types.ChatCompletionRequest) *claude.ClaudeRequest {
	claudeRequest := &claude.ClaudeRequest{
		Model:       request.Model,
		MaxTokens:   request.MaxTokens,
		Temperature: request.Temperature,
		TopP:        request.TopP,
		Stream:      request.Stream,
		Messages:    make([]claude.Message, 0),
	}

	// 处理系统消息
	var systemMessages []string
	var regularMessages []types.ChatCompletionMessage

	for _, msg := range request.Messages {
		if msg.Role == types.ChatMessageRoleSystem {
			if contentStr, ok := msg.Content.(string); ok {
				systemMessages = append(systemMessages, contentStr)
			}
		} else {
			regularMessages = append(regularMessages, msg)
		}
	}

	// 设置系统消息
	if len(systemMessages) > 0 {
		if len(systemMessages) == 1 {
			claudeRequest.System = systemMessages[0]
		} else {
			// 多个系统消息转换为数组格式
			systemArray := make([]interface{}, len(systemMessages))
			for i, msg := range systemMessages {
				systemArray[i] = map[string]interface{}{
					"type": "text",
					"text": msg,
				}
			}
			claudeRequest.System = systemArray
		}
	}

	// 转换常规消息
	for _, msg := range regularMessages {
		claudeMsg := claude.Message{
			Role: msg.Role,
		}

		// 处理消息内容
		if contentStr, ok := msg.Content.(string); ok {
			claudeMsg.Content = contentStr
		} else if contentParts, ok := msg.Content.([]types.ChatMessagePart); ok {
			// 处理多部分内容
			var parts []interface{}
			for _, part := range contentParts {
				if part.Type == "text" {
					parts = append(parts, map[string]interface{}{
						"type": "text",
						"text": part.Text,
					})
				}
				// 可以在这里添加对图像等其他类型的支持
			}
			claudeMsg.Content = parts
		}

		// 处理工具调用
		if len(msg.ToolCalls) > 0 {
			var parts []interface{}
			for _, toolCall := range msg.ToolCalls {
				parts = append(parts, map[string]interface{}{
					"type":  "tool_use",
					"id":    toolCall.Id,
					"name":  toolCall.Function.Name,
					"input": json.RawMessage(toolCall.Function.Arguments),
				})
			}
			claudeMsg.Content = parts
		}

		// 处理工具结果
		if msg.Role == types.ChatMessageRoleTool {
			claudeMsg.Content = []interface{}{
				map[string]interface{}{
					"type":        "tool_result",
					"tool_use_id": msg.ToolCallID,
					"content":     msg.Content,
				},
			}
		}

		claudeRequest.Messages = append(claudeRequest.Messages, claudeMsg)
	}

	// 处理工具定义
	if len(request.Tools) > 0 {
		tools := make([]claude.Tools, len(request.Tools))
		for i, tool := range request.Tools {
			tools[i] = claude.Tools{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				InputSchema: tool.Function.Parameters,
			}
		}
		claudeRequest.Tools = tools
	}

	// 处理停止序列
	if request.Stop != nil {
		if stopSlice, ok := request.Stop.([]string); ok {
			claudeRequest.StopSequences = stopSlice
		} else if stopStr, ok := request.Stop.(string); ok {
			claudeRequest.StopSequences = []string{stopStr}
		}
	}

	return claudeRequest
}

// convertClaudeToGeminiRequest 将 Claude 格式转换为 Gemini 格式
func convertClaudeToGeminiRequest(claudeRequest *claude.ClaudeRequest) *gemini.GeminiChatRequest {
	geminiRequest := &gemini.GeminiChatRequest{
		Model:    claudeRequest.Model,
		Contents: make([]gemini.GeminiChatContent, 0),
		GenerationConfig: gemini.GeminiChatGenerationConfig{
			MaxOutputTokens: claudeRequest.MaxTokens,
			Temperature:     claudeRequest.Temperature,
			TopP:            claudeRequest.TopP,
		},
		Stream: claudeRequest.Stream,
	}

	// 处理系统指令
	if claudeRequest.System != nil {
		if systemStr, ok := claudeRequest.System.(string); ok {
			geminiRequest.SystemInstruction = &gemini.GeminiChatContent{
				Parts: []gemini.GeminiPart{
					{Text: systemStr},
				},
			}
		} else if systemArray, ok := claudeRequest.System.([]interface{}); ok {
			var parts []gemini.GeminiPart
			for _, item := range systemArray {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if itemType, exists := itemMap["type"]; exists && itemType == "text" {
						if text, textExists := itemMap["text"]; textExists {
							if textStr, ok := text.(string); ok {
								parts = append(parts, gemini.GeminiPart{Text: textStr})
							}
						}
					}
				}
			}
			if len(parts) > 0 {
				geminiRequest.SystemInstruction = &gemini.GeminiChatContent{
					Parts: parts,
				}
			}
		}
	}

	// 转换消息
	for _, msg := range claudeRequest.Messages {
		role := "user"
		if msg.Role == "assistant" {
			role = "model"
		}

		var parts []gemini.GeminiPart

		// 处理消息内容
		if contentStr, ok := msg.Content.(string); ok {
			parts = append(parts, gemini.GeminiPart{Text: contentStr})
		} else if contentArray, ok := msg.Content.([]interface{}); ok {
			for _, content := range contentArray {
				if contentMap, ok := content.(map[string]interface{}); ok {
					contentType, _ := contentMap["type"].(string)

					switch contentType {
					case "text":
						if text, exists := contentMap["text"].(string); exists {
							parts = append(parts, gemini.GeminiPart{Text: text})
						}
					case "tool_use":
						if name, nameExists := contentMap["name"].(string); nameExists {
							if input, inputExists := contentMap["input"]; inputExists {
								if inputMap, ok := input.(map[string]interface{}); ok {
									parts = append(parts, gemini.GeminiPart{
										FunctionCall: &gemini.GeminiFunctionCall{
											Name: name,
											Args: inputMap,
										},
									})
								}
							}
						}
					case "tool_result":
						if content, contentExists := contentMap["content"]; contentExists {
							if toolUseId, idExists := contentMap["tool_use_id"].(string); idExists {
								parts = append(parts, gemini.GeminiPart{
									FunctionResponse: &gemini.GeminiFunctionResponse{
										Name: toolUseId,
										Response: map[string]interface{}{
											"content": content,
										},
									},
								})
							}
						}
					}
				}
			}
		}

		if len(parts) > 0 {
			geminiRequest.Contents = append(geminiRequest.Contents, gemini.GeminiChatContent{
				Role:  role,
				Parts: parts,
			})
		}
	}

	// 处理工具定义
	if len(claudeRequest.Tools) > 0 {
		var functionDeclarations []types.ChatCompletionFunction
		for _, tool := range claudeRequest.Tools {
			functionDeclarations = append(functionDeclarations, types.ChatCompletionFunction{
				Name:        tool.Name,
				Description: tool.Description,
				Parameters:  tool.InputSchema,
			})
		}

		// 将所有工具放在一个 GeminiChatTools 对象中
		geminiRequest.Tools = []gemini.GeminiChatTools{
			{
				FunctionDeclarations: functionDeclarations,
			},
		}
	}

	// 处理停止序列
	if len(claudeRequest.StopSequences) > 0 {
		geminiRequest.GenerationConfig.StopSequences = claudeRequest.StopSequences
	}

	return geminiRequest
}

// convertGeminiToClaudeResponse 将 Gemini 响应转换为 Claude 格式
func convertGeminiToClaudeResponse(geminiResponse *gemini.GeminiChatResponse, model string) *claude.ClaudeResponse {
	content := make([]claude.ResContent, 0)

	if len(geminiResponse.Candidates) > 0 {
		candidate := geminiResponse.Candidates[0]

		for _, part := range candidate.Content.Parts {
			if part.Text != "" {
				content = append(content, claude.ResContent{
					Type: "text",
					Text: part.Text,
				})
			} else if part.FunctionCall != nil {
				var input interface{}
				if part.FunctionCall.Args != nil {
					input = part.FunctionCall.Args
				} else {
					input = map[string]interface{}{}
				}

				content = append(content, claude.ResContent{
					Type:  "tool_use",
					Id:    part.FunctionCall.Name + "_" + generateRandomId(),
					Name:  part.FunctionCall.Name,
					Input: input,
				})
			}
		}
	}

	// 转换停止原因
	stopReason := "end_turn"
	if len(geminiResponse.Candidates) > 0 && geminiResponse.Candidates[0].FinishReason != nil {
		switch *geminiResponse.Candidates[0].FinishReason {
		case "STOP":
			stopReason = "end_turn"
		case "MAX_TOKENS":
			stopReason = "max_tokens"
		case "SAFETY":
			stopReason = "stop_sequence"
		default:
			stopReason = "end_turn"
		}
	}

	claudeResponse := &claude.ClaudeResponse{
		Id:           "msg_" + generateRandomId(),
		Type:         "message",
		Role:         "assistant",
		Content:      content,
		Model:        model,
		StopReason:   stopReason,
		StopSequence: "",
	}

	// 处理使用量信息
	if geminiResponse.UsageMetadata != nil {
		claudeResponse.Usage = claude.Usage{
			InputTokens:  geminiResponse.UsageMetadata.PromptTokenCount,
			OutputTokens: geminiResponse.UsageMetadata.CandidatesTokenCount,
		}
	}

	return claudeResponse
}

// convertClaudeToOpenAIResponse 将 Claude 响应转换为 OpenAI 格式
func convertClaudeToOpenAIResponse(claudeResponse *claude.ClaudeResponse, provider base.ProviderInterface, request *types.ChatCompletionRequest) (*types.ChatCompletionResponse, *types.OpenAIErrorWithStatusCode) {
	var content interface{}
	var toolCalls []*types.ChatCompletionToolCalls

	// 处理内容
	var textParts []string
	for _, resContent := range claudeResponse.Content {
		if resContent.Type == "text" {
			textParts = append(textParts, resContent.Text)
		} else if resContent.Type == "tool_use" {
			var args string
			if resContent.Input != nil {
				argsBytes, _ := json.Marshal(resContent.Input)
				args = string(argsBytes)
			} else {
				args = "{}"
			}

			toolCall := &types.ChatCompletionToolCalls{
				Id:   resContent.Id,
				Type: types.ChatMessageRoleFunction,
				Function: &types.ChatCompletionToolCallsFunction{
					Name:      resContent.Name,
					Arguments: args,
				},
			}
			toolCalls = append(toolCalls, toolCall)
		}
	}

	// 设置内容
	if len(textParts) > 0 {
		content = strings.Join(textParts, "")
	}

	// 转换停止原因
	finishReason := "stop"
	switch claudeResponse.StopReason {
	case "end_turn":
		finishReason = "stop"
	case "max_tokens":
		finishReason = "length"
	case "tool_use":
		finishReason = "tool_calls"
	case "stop_sequence":
		finishReason = "content_filter"
	}

	choice := types.ChatCompletionChoice{
		Index: 0,
		Message: types.ChatCompletionMessage{
			Role:      types.ChatMessageRoleAssistant,
			Content:   content,
			ToolCalls: toolCalls,
		},
		FinishReason: finishReason,
	}

	response := &types.ChatCompletionResponse{
		ID:      claudeResponse.Id,
		Object:  "chat.completion",
		Created: 0, // 可以设置为当前时间戳
		Model:   claudeResponse.Model,
		Choices: []types.ChatCompletionChoice{choice},
	}

	// 设置使用量信息
	if provider.GetUsage() != nil {
		response.Usage = provider.GetUsage()
		response.Usage.PromptTokens = claudeResponse.Usage.InputTokens
		response.Usage.CompletionTokens = claudeResponse.Usage.OutputTokens
		response.Usage.TotalTokens = claudeResponse.Usage.InputTokens + claudeResponse.Usage.OutputTokens
	}

	return response, nil
}

// GeminiClaudeStreamHandler 处理 Gemini Claude 格式的流式响应
// 参考 VertexAI 的 GeminiRelayStreamHandler 实现
type GeminiClaudeStreamHandler struct {
	Usage           *types.Usage
	Request         *claude.ClaudeRequest
	Prefix          string
	ModelName       string
	buffer          []byte // 用于累积 JSON 数据
	messageStarted  bool   // 标记是否已发送 message_start
	lastUsage       *types.Usage // 保存最后的 usage 信息
	processedCount  int    // 已处理的对象计数
}

// HandlerStream 处理流式数据转换
// VertexAI 返回的是 JSON 数组格式，需要特殊处理
func (h *GeminiClaudeStreamHandler) HandlerStream(rawLine *[]byte, dataChan chan string, errChan chan error) {
	if rawLine == nil || len(*rawLine) == 0 {
		return
	}

	rawStr := string(*rawLine)
	if logger.Logger != nil {
		logger.SysLog("GeminiClaudeStreamHandler: Processing raw line: " + rawStr)
	}

	// 检查是否是数据前缀，如果不是则直接转发
	if !strings.HasPrefix(rawStr, h.Prefix) && !strings.Contains(rawStr, "{") {
		if logger.Logger != nil {
			logger.SysLog("GeminiClaudeStreamHandler: No data prefix, forwarding as-is")
		}
		*rawLine = nil
		return
	}

	// VertexAI 流式响应是 JSON 数组格式，不是标准的 SSE
	// 我们需要累积完整的 JSON 对象
	if h.buffer == nil {
		h.buffer = make([]byte, 0)
	}

	// 累积数据
	h.buffer = append(h.buffer, *rawLine...)

	// 尝试解析完整的 JSON 对象
	h.tryParseCompleteJSON(dataChan, errChan)

	// 清空当前行，避免重复处理
	*rawLine = nil
}

// tryParseCompleteJSON 尝试解析完整的 JSON 对象
func (h *GeminiClaudeStreamHandler) tryParseCompleteJSON(dataChan chan string, errChan chan error) {
	bufferStr := string(h.buffer)

	// 查找完整的 JSON 对象
	// VertexAI 返回的格式类似：[{...}, {...}]
	var jsonObjects []string

	// 简单的 JSON 对象分割逻辑
	depth := 0
	start := -1
	inString := false
	escaped := false

	for i, char := range bufferStr {
		if escaped {
			escaped = false
			continue
		}

		if char == '\\' && inString {
			escaped = true
			continue
		}

		if char == '"' {
			inString = !inString
			continue
		}

		if !inString {
			if char == '{' {
				if depth == 0 {
					start = i
				}
				depth++
			} else if char == '}' {
				depth--
				if depth == 0 && start >= 0 {
					// 找到完整的 JSON 对象
					jsonObj := bufferStr[start : i+1]
					jsonObjects = append(jsonObjects, jsonObj)

					// 处理这个 JSON 对象
					h.processGeminiJSON(jsonObj, dataChan, errChan)

					// 从缓冲区中移除已处理的部分
					h.buffer = h.buffer[i+1:]
					bufferStr = string(h.buffer)
					start = -1

					// 重新开始解析
					return
				}
			}
		}
	}
}

// processGeminiJSON 处理单个 Gemini JSON 对象
func (h *GeminiClaudeStreamHandler) processGeminiJSON(jsonStr string, dataChan chan string, errChan chan error) {
	// logger.SysLog("GeminiClaudeStreamHandler: Processing JSON object: " + jsonStr)

	// 解析 Gemini 响应
	var geminiResponse gemini.GeminiChatResponse
	err := json.Unmarshal([]byte(jsonStr), &geminiResponse)
	if err != nil {
		// logger.SysError("GeminiClaudeStreamHandler: Failed to parse Gemini JSON: " + err.Error())
		errChan <- common.ErrorToOpenAIError(err)
		return
	}

	// 检查错误
	if geminiResponse.ErrorInfo != nil {
		// logger.SysError("GeminiClaudeStreamHandler: Gemini response contains error")
		errChan <- geminiResponse.ErrorInfo
		return
	}

	// 更新 Usage 信息
	if geminiResponse.UsageMetadata != nil && h.Usage != nil {
		// logger.SysLog("GeminiClaudeStreamHandler: Updating usage metadata")
		h.Usage.PromptTokens = geminiResponse.UsageMetadata.PromptTokenCount
		h.Usage.CompletionTokens = geminiResponse.UsageMetadata.CandidatesTokenCount + geminiResponse.UsageMetadata.ThoughtsTokenCount
		h.Usage.CompletionTokensDetails.ReasoningTokens = geminiResponse.UsageMetadata.ThoughtsTokenCount
		h.Usage.TotalTokens = geminiResponse.UsageMetadata.TotalTokenCount

		// logger.SysLog("GeminiClaudeStreamHandler: Usage updated - PromptTokens: " +
		//	fmt.Sprintf("%d", h.Usage.PromptTokens) + ", CompletionTokens: " +
		//	fmt.Sprintf("%d", h.Usage.CompletionTokens) + ", TotalTokens: " +
		//	fmt.Sprintf("%d", h.Usage.TotalTokens))
	}

	// 转换为 Claude 格式并发送流式事件
	h.convertGeminiToClaudeStream(&geminiResponse, dataChan)
}

// convertGeminiToClaudeStream 将 Gemini 响应转换为 Claude 流式格式
func (h *GeminiClaudeStreamHandler) convertGeminiToClaudeStream(geminiResponse *gemini.GeminiChatResponse, dataChan chan string) {
	if logger.Logger != nil {
		logger.SysLog("GeminiClaudeStreamHandler: Converting Gemini response to Claude stream")
	}

	// 转换为 Claude 格式
	claudeResponse := ConvertGeminiToClaudeResponse(geminiResponse, h.ModelName)

	// logger.SysLog("GeminiClaudeStreamHandler: Converted to Claude response with " + fmt.Sprintf("%d", len(claudeResponse.Content)) + " content blocks")
	// 发送 message_start 事件
	messageStart := map[string]interface{}{
		"type": "message_start",
		"message": map[string]interface{}{
			"id":            claudeResponse.Id,
			"type":          "message",
			"role":          "assistant",
			"content":       []interface{}{},
			"model":         claudeResponse.Model,
			"stop_reason":   nil,
			"stop_sequence": nil,
			"usage": map[string]interface{}{
				"input_tokens":  claudeResponse.Usage.InputTokens,
				"output_tokens": 1,
			},
		},
	}

	messageStartBytes, _ := json.Marshal(messageStart)
	dataChan <- string(messageStartBytes)

	// 处理内容块
	for i, content := range claudeResponse.Content {
		// 发送 content_block_start 事件
		contentBlockStart := map[string]interface{}{
			"type":  "content_block_start",
			"index": i,
		}

		if content.Type == "text" {
			contentBlockStart["content_block"] = map[string]interface{}{
				"type": "text",
				"text": "",
			}
		} else if content.Type == "tool_use" {
			contentBlockStart["content_block"] = map[string]interface{}{
				"type":  "tool_use",
				"id":    content.Id,
				"name":  content.Name,
				"input": map[string]interface{}{},
			}
		}

		contentBlockStartBytes, _ := json.Marshal(contentBlockStart)
		dataChan <- string(contentBlockStartBytes)

		// 发送 content_block_delta 事件
		if content.Type == "text" {
			contentBlockDelta := map[string]interface{}{
				"type":  "content_block_delta",
				"index": i,
				"delta": map[string]interface{}{
					"type": "text_delta",
					"text": content.Text,
				},
			}
			contentBlockDeltaBytes, _ := json.Marshal(contentBlockDelta)
			dataChan <- string(contentBlockDeltaBytes)
		} else if content.Type == "tool_use" {
			inputBytes, _ := json.Marshal(content.Input)
			contentBlockDelta := map[string]interface{}{
				"type":  "content_block_delta",
				"index": i,
				"delta": map[string]interface{}{
					"type":         "input_json_delta",
					"partial_json": string(inputBytes),
				},
			}
			contentBlockDeltaBytes, _ := json.Marshal(contentBlockDelta)
			dataChan <- string(contentBlockDeltaBytes)
		}

		// 发送 content_block_stop 事件
		contentBlockStop := map[string]interface{}{
			"type":  "content_block_stop",
			"index": i,
		}
		contentBlockStopBytes, _ := json.Marshal(contentBlockStop)
		dataChan <- string(contentBlockStopBytes)
	}

	// 发送 message_delta 事件
	messageDelta := map[string]interface{}{
		"type": "message_delta",
		"delta": map[string]interface{}{
			"stop_reason":   claudeResponse.StopReason,
			"stop_sequence": claudeResponse.StopSequence,
		},
		"usage": map[string]interface{}{
			"input_tokens":  claudeResponse.Usage.InputTokens,
			"output_tokens": claudeResponse.Usage.OutputTokens,
		},
	}
	messageDeltaBytes, _ := json.Marshal(messageDelta)
	dataChan <- string(messageDeltaBytes)

	// 发送 message_stop 事件
	messageStop := map[string]interface{}{
		"type": "message_stop",
	}
	messageStopBytes, _ := json.Marshal(messageStop)
	dataChan <- string(messageStopBytes)
}

// 辅助函数
func generateRandomId() string {
	return strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(
		"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx", "x", "a"), "y", "b"), "-", "")[:15]
}

func GetGeminiClaudeModelName(modelName string) string {
	// 保持原始模型名
	return modelName
}

func getGeminiClaudeOtherUrl(stream bool) string {
	if stream {
		return "streamGenerateContent"
	}
	return "generateContent"
}

// cleanSchemaForVertexAI 清理 schema 中 VertexAI 不支持的字段
// 参考 demo/gemini.transformer.js 第 57-86 行的逻辑
func cleanSchemaForVertexAI(schema interface{}) interface{} {
	if schema == nil {
		return nil
	}

	// 深拷贝 schema 以避免修改原始数据
	schemaBytes, err := json.Marshal(schema)
	if err != nil {
		logger.SysError("cleanSchemaForVertexAI: Failed to marshal schema: " + err.Error())
		return schema
	}

	var cleanedSchema map[string]interface{}
	if err := json.Unmarshal(schemaBytes, &cleanedSchema); err != nil {
		logger.SysError("cleanSchemaForVertexAI: Failed to unmarshal schema: " + err.Error())
		return schema
	}

	// 参考 gemini.transformer.js 的清理逻辑
	cleanSchemaLikeGeminiTransformer(cleanedSchema)

	return cleanedSchema
}

// cleanSchemaLikeGeminiTransformer 按照 gemini.transformer.js 的逻辑清理 schema
func cleanSchemaLikeGeminiTransformer(params map[string]interface{}) {
	// 删除顶级不支持的字段（参考 gemini.transformer.js 第 57-58 行）
	delete(params, "$schema")
	delete(params, "additionalProperties")
	// 注意：gemini.transformer.js 没有删除 title，但 VertexAI 有时不支持，我们保持删除
	// delete(params, "title")

	// 处理 properties（参考 gemini.transformer.js 第 59-86 行）
	if properties, ok := params["properties"].(map[string]interface{}); ok {
		for key, prop := range properties {
			if propMap, ok := prop.(map[string]interface{}); ok {
				// 删除属性级别的不支持字段（参考第 61-63 行）
				delete(propMap, "$schema")
				delete(propMap, "additionalProperties")

				// 处理 items（数组类型的属性）（参考第 64-71 行）
				if items, ok := propMap["items"]; ok {
					if itemsMap, ok := items.(map[string]interface{}); ok {
						delete(itemsMap, "$schema")
						delete(itemsMap, "additionalProperties")
					}
				}

				// 处理 string 类型的 format 字段（参考第 72-79 行）
				if propType, ok := propMap["type"].(string); ok && propType == "string" {
					if format, ok := propMap["format"].(string); ok {
						// 只保留 enum 和 date-time 格式
						if format != "enum" && format != "date-time" {
							delete(propMap, "format")
						}
					}
				}

				properties[key] = propMap
			}
		}
		params["properties"] = properties
	}
}
