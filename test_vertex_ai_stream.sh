#!/bin/bash

# 测试 Vertex AI 流式请求的脚本
# 用于验证修复后的流式响应是否正确处理 usage 字段和避免重复消息

echo "Testing Vertex AI streaming with Claude API format..."

# 设置测试参数
API_URL="http://localhost:3000/claude/v1/messages"
API_KEY="your-api-key"  # 请替换为实际的 API key

# 测试请求体
REQUEST_BODY='{
  "model": "gemini-2.5-pro",
  "max_tokens": 1000,
  "stream": true,
  "messages": [
    {
      "role": "user",
      "content": "请简单介绍一下人工智能的发展历史，大约200字。"
    }
  ]
}'

echo "Sending streaming request..."
echo "Request body: $REQUEST_BODY"
echo ""
echo "Response:"
echo "=========================================="

# 发送流式请求并显示响应
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Accept: text/event-stream" \
  -d "$REQUEST_BODY" \
  --no-buffer \
  -v

echo ""
echo "=========================================="
echo "Test completed."
